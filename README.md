# FCM Message Receiver Application

A Node.js application that uses Firebase Cloud Messaging (FCM) to receive and log messages. This application demonstrates how to use the `onMessage` method to catch messages from FCM directly in your application.

## Prerequisites

- Node.js (v16 or higher)
- A Firebase project with Cloud Messaging enabled

## Setup

1. Clone this repository
2. Install dependencies:
   ```
   npm install
   ```
3. Configure Firebase:
   - Go to the [Firebase Console](https://console.firebase.google.com/)
   - Create a new project or select an existing one
   - Add a web app to your project
   - Copy the Firebase configuration object
   - Replace the placeholder configuration in the following files:
     - `src/fcm-client.js`
     - `public/index.html`
     - `public/firebase-messaging-sw.js`

4. Set up VAPID key for web push notifications:
   - In the Firebase Console, go to Project Settings > Cloud Messaging
   - Generate a new Web Push certificate
   - Copy the key pair
   - Replace `YOUR_VAPID_KEY` in `src/fcm-client.js` and `public/index.html`

5. (Optional) For server-side operations with Firebase Admin SDK:
   - Go to Project Settings > Service Accounts
   - Click "Generate new private key"
   - Save the JSON file as `serviceAccountKey.json` in the root directory of this project

## Running the Application

Start the server:

```
npm start
```

The server will start on port 3000 (or the port specified in the PORT environment variable).

Open your browser and navigate to `http://localhost:3000` to access the web interface.

## How It Works

This application demonstrates both receiving and sending FCM messages:

### Receiving FCM Messages

1. **Browser-based FCM client** - Uses the Firebase Messaging SDK's `onMessage` method to receive and display messages in real-time when the browser is open.

2. **Express.js server endpoint** - Provides a REST API endpoint that can receive messages via HTTP POST requests.

### Sending FCM Messages

The application includes a complete API for sending FCM messages:

1. **Send to specific device** - Send a message to a single device using its FCM token
2. **Send to multiple devices** - Send a message to multiple devices at once
3. **Send to topic** - Send a message to all devices subscribed to a topic
4. **Topic management** - Subscribe and unsubscribe devices to/from topics

### Key Components

- **src/fcm-client.js**: Contains the Firebase client-side messaging setup with the `onMessage` handler
- **public/index.html**: Web interface that demonstrates FCM message reception in the browser
- **public/firebase-messaging-sw.js**: Service worker for handling background notifications
- **src/index.js**: Express.js server that handles HTTP endpoints and serves the web interface
- **src/fcm-sender.js**: API for sending FCM messages to devices and topics
- **public/fcm-sender.html**: Web interface for sending FCM messages

## Testing

### Testing Browser-based FCM Reception

1. Open the application in your browser at `http://localhost:3000`
2. Click "Request Notification Permission" if prompted
3. Note the FCM token displayed on the page
4. Use the Firebase Console to send a test message to this token:
   - Go to your Firebase project
   - Navigate to Messaging > Send your first message
   - Select "Send test message"
   - Enter the FCM token
   - Fill in the notification details
   - Send the message

### Testing via HTTP Endpoint

You can also test the application by sending a POST request to the `/fcm/receive` endpoint:

```bash
curl -X POST http://localhost:3000/fcm/receive \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "key": "value",
      "customField": "customValue"
    },
    "notification": {
      "title": "Test Title",
      "body": "Test Body"
    },
    "priority": "high",
    "timestamp": "2023-07-10T15:30:00Z"
  }'
```

## Using the FCM Sender API

The application includes a built-in FCM Sender API that you can use to send messages. You can access it in two ways:

### 1. Web Interface

Navigate to `http://localhost:3000/fcm-sender.html` to use the web interface for sending FCM messages. The interface provides forms for:

- Sending to a single device
- Sending to multiple devices
- Sending to a topic
- Managing topic subscriptions

### 2. REST API

You can also use the REST API directly:

#### Send to a Single Device

```bash
curl -X POST http://localhost:3000/api/fcm/send-to-device \
  -H "Content-Type: application/json" \
  -d '{
    "token": "FCM_TOKEN",
    "title": "Test Title",
    "body": "Test Body",
    "data": {
      "key": "value",
      "customField": "customValue"
    }
  }'
```

#### Send to Multiple Devices

```bash
curl -X POST http://localhost:3000/api/fcm/send-to-devices \
  -H "Content-Type: application/json" \
  -d '{
    "tokens": ["FCM_TOKEN_1", "FCM_TOKEN_2"],
    "title": "Test Title",
    "body": "Test Body",
    "data": {
      "key": "value",
      "customField": "customValue"
    }
  }'
```

#### Send to a Topic

```bash
curl -X POST http://localhost:3000/api/fcm/send-to-topic \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "news",
    "title": "Test Title",
    "body": "Test Body",
    "data": {
      "key": "value",
      "customField": "customValue"
    }
  }'
```

#### Subscribe Devices to a Topic

```bash
curl -X POST http://localhost:3000/api/fcm/subscribe-to-topic \
  -H "Content-Type: application/json" \
  -d '{
    "tokens": ["FCM_TOKEN_1", "FCM_TOKEN_2"],
    "topic": "news"
  }'
```

#### Unsubscribe Devices from a Topic

```bash
curl -X POST http://localhost:3000/api/fcm/unsubscribe-from-topic \
  -H "Content-Type: application/json" \
  -d '{
    "tokens": ["FCM_TOKEN_1", "FCM_TOKEN_2"],
    "topic": "news"
  }'
```

## Sending FCM Messages from Another Application

If you want to send FCM messages from a completely separate application, you can use the Firebase Admin SDK:

```javascript
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK with your service account
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

// The FCM token of the target device (from the web interface)
const token = 'FCM_TOKEN_FROM_WEB_INTERFACE';

// Message payload
const message = {
  data: {
    key: 'value',
    customField: 'customValue'
  },
  notification: {
    title: 'Test Title',
    body: 'Test Body'
  },
  token: token
};

// Send the message
admin.messaging().send(message)
  .then((response) => {
    console.log('Successfully sent message:', response);
  })
  .catch((error) => {
    console.error('Error sending message:', error);
  });
```

## License

ISC
