<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FCM Sender</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    .container {
      border: 1px solid #ddd;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    h1, h2 {
      color: #333;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input, textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    textarea {
      height: 100px;
      font-family: monospace;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    .response {
      margin-top: 20px;
      padding: 10px;
      background-color: #f4f4f4;
      border-radius: 5px;
      white-space: pre-wrap;
      font-family: monospace;
      max-height: 300px;
      overflow: auto;
    }
    .tabs {
      display: flex;
      margin-bottom: 20px;
      border-bottom: 1px solid #ddd;
    }
    .tab {
      padding: 10px 15px;
      cursor: pointer;
      margin-right: 5px;
      border: 1px solid #ddd;
      border-bottom: none;
      border-radius: 4px 4px 0 0;
      background-color: #f4f4f4;
    }
    .tab.active {
      background-color: white;
      border-bottom: 1px solid white;
      margin-bottom: -1px;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .note {
      background-color: #fffde7;
      padding: 10px;
      border-left: 4px solid #ffd600;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>FCM Sender</h1>
    <p>Use this interface to send Firebase Cloud Messages (FCM) to devices or topics.</p>

    <div class="note">
      <strong>Note:</strong> You need a valid FCM token or topic to send messages. You can get an FCM token from the <a href="/">FCM Receiver</a> page.
    </div>

    <div class="tabs">
      <div class="tab active" data-tab="device">Send to Device</div>
      <div class="tab" data-tab="direct">Direct Send</div>
      <div class="tab" data-tab="devices">Send to Multiple Devices</div>
      <div class="tab" data-tab="topic">Send to Topic</div>
      <div class="tab" data-tab="subscribe">Subscribe to Topic</div>
      <div class="tab" data-tab="unsubscribe">Unsubscribe from Topic</div>
    </div>

    <!-- Send to Device Form -->
    <div id="device-tab" class="tab-content active">
      <h2>Send to Device</h2>
      <form id="device-form">
        <div class="form-group">
          <label for="device-token">FCM Token:</label>
          <input type="text" id="device-token" name="token" required placeholder="Enter the FCM token of the target device">
        </div>
        <div class="form-group">
          <label for="device-title">Notification Title:</label>
          <input type="text" id="device-title" name="title" placeholder="Enter notification title">
        </div>
        <div class="form-group">
          <label for="device-body">Notification Body:</label>
          <input type="text" id="device-body" name="body" placeholder="Enter notification body">
        </div>
        <div class="form-group">
          <label for="device-data">Data Payload (JSON):</label>
          <textarea id="device-data" name="data" placeholder='{"key1": "value1", "key2": "value2"}'></textarea>
        </div>
        <button type="submit">Send Message</button>
      </form>
      <div id="device-response" class="response"></div>
    </div>

    <!-- Direct Send Form -->
    <div id="direct-tab" class="tab-content">
      <h2>Direct Send (Alternative API)</h2>
      <p>This uses a direct API endpoint that bypasses the FCM sender router.</p>
      <form id="direct-form">
        <div class="form-group">
          <label for="direct-token">FCM Token:</label>
          <input type="text" id="direct-token" name="token" required placeholder="Enter the FCM token of the target device">
        </div>
        <div class="form-group">
          <label for="direct-title">Notification Title:</label>
          <input type="text" id="direct-title" name="title" placeholder="Enter notification title">
        </div>
        <div class="form-group">
          <label for="direct-body">Notification Body:</label>
          <input type="text" id="direct-body" name="body" placeholder="Enter notification body">
        </div>
        <div class="form-group">
          <label for="direct-data">Data Payload (JSON):</label>
          <textarea id="direct-data" name="data" placeholder='{"key1": "value1", "key2": "value2"}'></textarea>
        </div>
        <button type="submit">Send Message</button>
      </form>
      <div id="direct-response" class="response"></div>
    </div>

    <!-- Send to Multiple Devices Form -->
    <div id="devices-tab" class="tab-content">
      <h2>Send to Multiple Devices</h2>
      <form id="devices-form">
        <div class="form-group">
          <label for="device-token">FCM Token:</label>
          <input type="text" id="device-token" name="token" required placeholder="Enter the FCM token of the target device">
        </div>
        <div class="form-group">
          <label for="device-title">Notification Title:</label>
          <input type="text" id="device-title" name="title" placeholder="Enter notification title">
        </div>
        <div class="form-group">
          <label for="device-body">Notification Body:</label>
          <input type="text" id="device-body" name="body" placeholder="Enter notification body">
        </div>
        <div class="form-group">
          <label for="device-data">Data Payload (JSON):</label>
          <textarea id="device-data" name="data" placeholder='{"key1": "value1", "key2": "value2"}'></textarea>
        </div>
        <button type="submit">Send Message</button>
      </form>
      <div id="device-response" class="response"></div>
    </div>

    <!-- Send to Multiple Devices Form -->
    <div id="devices-tab" class="tab-content">
      <h2>Send to Multiple Devices</h2>
      <form id="devices-form">
        <div class="form-group">
          <label for="devices-tokens">FCM Tokens (one per line):</label>
          <textarea id="devices-tokens" name="tokens" required placeholder="Enter FCM tokens, one per line"></textarea>
        </div>
        <div class="form-group">
          <label for="devices-title">Notification Title:</label>
          <input type="text" id="devices-title" name="title" placeholder="Enter notification title">
        </div>
        <div class="form-group">
          <label for="devices-body">Notification Body:</label>
          <input type="text" id="devices-body" name="body" placeholder="Enter notification body">
        </div>
        <div class="form-group">
          <label for="devices-data">Data Payload (JSON):</label>
          <textarea id="devices-data" name="data" placeholder='{"key1": "value1", "key2": "value2"}'></textarea>
        </div>
        <button type="submit">Send Message</button>
      </form>
      <div id="devices-response" class="response"></div>
    </div>

    <!-- Send to Topic Form -->
    <div id="topic-tab" class="tab-content">
      <h2>Send to Topic</h2>
      <form id="topic-form">
        <div class="form-group">
          <label for="topic-name">Topic Name:</label>
          <input type="text" id="topic-name" name="topic" required placeholder="Enter the topic name">
        </div>
        <div class="form-group">
          <label for="topic-title">Notification Title:</label>
          <input type="text" id="topic-title" name="title" placeholder="Enter notification title">
        </div>
        <div class="form-group">
          <label for="topic-body">Notification Body:</label>
          <input type="text" id="topic-body" name="body" placeholder="Enter notification body">
        </div>
        <div class="form-group">
          <label for="topic-data">Data Payload (JSON):</label>
          <textarea id="topic-data" name="data" placeholder='{"key1": "value1", "key2": "value2"}'></textarea>
        </div>
        <button type="submit">Send Message</button>
      </form>
      <div id="topic-response" class="response"></div>
    </div>

    <!-- Subscribe to Topic Form -->
    <div id="subscribe-tab" class="tab-content">
      <h2>Subscribe to Topic</h2>
      <form id="subscribe-form">
        <div class="form-group">
          <label for="subscribe-tokens">FCM Tokens (one per line):</label>
          <textarea id="subscribe-tokens" name="tokens" required placeholder="Enter FCM tokens, one per line"></textarea>
        </div>
        <div class="form-group">
          <label for="subscribe-topic">Topic Name:</label>
          <input type="text" id="subscribe-topic" name="topic" required placeholder="Enter the topic name">
        </div>
        <button type="submit">Subscribe</button>
      </form>
      <div id="subscribe-response" class="response"></div>
    </div>

    <!-- Unsubscribe from Topic Form -->
    <div id="unsubscribe-tab" class="tab-content">
      <h2>Unsubscribe from Topic</h2>
      <form id="unsubscribe-form">
        <div class="form-group">
          <label for="unsubscribe-tokens">FCM Tokens (one per line):</label>
          <textarea id="unsubscribe-tokens" name="tokens" required placeholder="Enter FCM tokens, one per line"></textarea>
        </div>
        <div class="form-group">
          <label for="unsubscribe-topic">Topic Name:</label>
          <input type="text" id="unsubscribe-topic" name="topic" required placeholder="Enter the topic name">
        </div>
        <button type="submit">Unsubscribe</button>
      </form>
      <div id="unsubscribe-response" class="response"></div>
    </div>
  </div>

  <script>
    // Tab switching functionality
    document.querySelectorAll('.tab').forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs and tab contents
        document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));

        // Add active class to clicked tab and corresponding content
        tab.classList.add('active');
        document.getElementById(`${tab.dataset.tab}-tab`).classList.add('active');
      });
    });

    // Helper function to parse JSON safely
    function safeParseJSON(json) {
      try {
        return json ? JSON.parse(json) : {};
      } catch (e) {
        return {};
      }
    }

    // Helper function to handle form submission
    async function handleFormSubmit(formId, endpoint, responseId, isDirectApi = false) {
      const form = document.getElementById(formId);
      const responseElement = document.getElementById(responseId);

      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        responseElement.textContent = 'Sending request...';

        // Get form data
        const formData = new FormData(form);
        const data = {};

        for (const [key, value] of formData.entries()) {
          if (key === 'tokens' && value.includes('\n')) {
            // Handle tokens as array (split by newline)
            data[key] = value.split('\n').filter(token => token.trim() !== '');
          } else if (key === 'data') {
            // Parse data as JSON
            data[key] = safeParseJSON(value);
          } else {
            data[key] = value;
          }
        }

        try {
          // Determine API URL based on whether it's a direct API or not
          const apiUrl = isDirectApi ? `/api/direct/${endpoint}` : `/api/fcm/${endpoint}`;

          // Send request to API
          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
          });

          // Parse response
          const result = await response.json();

          // Display response
          responseElement.textContent = JSON.stringify(result, null, 2);
        } catch (error) {
          responseElement.textContent = `Error: ${error.message}`;
        }
      });
    }

    // Set up form handlers
    document.addEventListener('DOMContentLoaded', () => {
      handleFormSubmit('device-form', 'send-to-device', 'device-response');
      handleFormSubmit('direct-form', 'send', 'direct-response', true); // Direct API endpoint
      handleFormSubmit('devices-form', 'send-to-devices', 'devices-response');
      handleFormSubmit('topic-form', 'send-to-topic', 'topic-response');
      handleFormSubmit('subscribe-form', 'subscribe-to-topic', 'subscribe-response');
      handleFormSubmit('unsubscribe-form', 'unsubscribe-from-topic', 'unsubscribe-response');
    });
  </script>
</body>
</html>
