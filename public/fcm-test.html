<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FCM Test - Simple Approach</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    .container {
      border: 1px solid #ddd;
      padding: 20px;
      border-radius: 5px;
    }
    h1, h2 {
      color: #333;
    }
    button {
      background-color: #4CAF50;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-top: 10px;
    }
    button:hover {
      background-color: #45a049;
    }
    .note {
      background-color: #fffde7;
      padding: 10px;
      border-left: 4px solid #ffd600;
      margin: 20px 0;
    }
    .error {
      background-color: #ffebee;
      padding: 10px;
      border-left: 4px solid #f44336;
      margin: 20px 0;
    }
    .success {
      background-color: #e8f5e9;
      padding: 10px;
      border-left: 4px solid #4CAF50;
      margin: 20px 0;
    }
    pre {
      background-color: #f4f4f4;
      padding: 10px;
      border-radius: 5px;
      overflow: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    #messageLog {
      margin-top: 20px;
    }
    .step {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .step h3 {
      margin-top: 0;
      color: #2196F3;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>FCM Test - Simplified Approach</h1>
    <p>
      This is a simplified test page that helps diagnose FCM token registration issues.
      <a href="/" style="color: #4CAF50; font-weight: bold;">Go back to main FCM Receiver</a>
    </p>

    <div class="step">
      <h3>Step 1: Check Browser Support</h3>
      <button onclick="checkBrowserSupport()">Check Browser Support</button>
      <div id="browserSupport"></div>
    </div>

    <div class="step">
      <h3>Step 2: Request Notification Permission</h3>
      <button onclick="requestNotificationPermission()">Request Permission</button>
      <div id="permissionStatus"></div>
    </div>

    <div class="step">
      <h3>Step 3: Register Service Worker</h3>
      <button onclick="registerServiceWorker()">Register Service Worker</button>
      <div id="serviceWorkerStatus"></div>
    </div>

    <div class="step">
      <h3>Step 4: Test Push Subscription (Without Firebase)</h3>
      <button onclick="testPushSubscription()">Test Push Subscription</button>
      <div id="pushSubscriptionStatus"></div>
    </div>

    <div class="step">
      <h3>Step 5: Initialize Firebase (If Steps 1-4 Work)</h3>
      <button onclick="initializeFirebase()">Initialize Firebase</button>
      <div id="firebaseStatus"></div>
    </div>

    <div class="step">
      <h3>Step 6: Get FCM Token</h3>
      <button onclick="getFCMToken()">Get FCM Token</button>
      <div id="tokenStatus"></div>
    </div>

    <h2>Debug Information</h2>
    <pre id="debugLog">Debug information will appear here...</pre>
  </div>

  <script>
    let debugLog = [];
    let serviceWorkerRegistration = null;
    let messaging = null;

    function log(message) {
      const timestamp = new Date().toISOString();
      const logMessage = `[${timestamp}] ${message}`;
      debugLog.push(logMessage);
      document.getElementById('debugLog').textContent = debugLog.join('\n');
      console.log(logMessage);
    }

    function updateStatus(elementId, message, isError = false, isSuccess = false) {
      const element = document.getElementById(elementId);
      element.innerHTML = `<div class="${isError ? 'error' : isSuccess ? 'success' : 'note'}">${message}</div>`;
    }

    async function checkBrowserSupport() {
      log('Checking browser support...');
      
      const checks = {
        'Service Workers': 'serviceWorker' in navigator,
        'Push Messaging': 'PushManager' in window,
        'Notifications': 'Notification' in window,
        'IndexedDB': 'indexedDB' in window
      };

      let allSupported = true;
      let message = '<strong>Browser Support Check:</strong><ul>';
      
      for (const [feature, supported] of Object.entries(checks)) {
        message += `<li>${feature}: ${supported ? '✅ Supported' : '❌ Not Supported'}</li>`;
        if (!supported) allSupported = false;
        log(`${feature}: ${supported ? 'Supported' : 'Not Supported'}`);
      }
      
      message += '</ul>';
      
      if (allSupported) {
        message += '<p>✅ All required features are supported!</p>';
        updateStatus('browserSupport', message, false, true);
      } else {
        message += '<p>❌ Some required features are not supported. FCM may not work properly.</p>';
        updateStatus('browserSupport', message, true);
      }
    }

    async function requestNotificationPermission() {
      log('Requesting notification permission...');
      
      try {
        const permission = await Notification.requestPermission();
        log(`Notification permission: ${permission}`);
        
        if (permission === 'granted') {
          updateStatus('permissionStatus', '✅ Notification permission granted!', false, true);
        } else if (permission === 'denied') {
          updateStatus('permissionStatus', '❌ Notification permission denied. Please enable notifications in your browser settings.', true);
        } else {
          updateStatus('permissionStatus', '⚠️ Notification permission is default (not granted or denied).', false);
        }
      } catch (error) {
        log(`Error requesting permission: ${error.message}`);
        updateStatus('permissionStatus', `❌ Error requesting permission: ${error.message}`, true);
      }
    }

    async function registerServiceWorker() {
      log('Registering service worker...');
      
      if (!('serviceWorker' in navigator)) {
        updateStatus('serviceWorkerStatus', '❌ Service workers not supported', true);
        return;
      }

      try {
        // First, unregister any existing service workers
        const registrations = await navigator.serviceWorker.getRegistrations();
        log(`Found ${registrations.length} existing service worker registrations`);
        
        for (const registration of registrations) {
          log(`Unregistering service worker: ${registration.scope}`);
          await registration.unregister();
        }

        // Register new service worker
        log('Registering new service worker...');
        serviceWorkerRegistration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
        log(`Service worker registered with scope: ${serviceWorkerRegistration.scope}`);
        
        // Wait for activation
        if (serviceWorkerRegistration.installing) {
          log('Waiting for service worker to activate...');
          await new Promise(resolve => {
            serviceWorkerRegistration.installing.addEventListener('statechange', e => {
              if (e.target.state === 'activated') {
                log('Service worker activated');
                resolve();
              }
            });
          });
        }
        
        updateStatus('serviceWorkerStatus', '✅ Service worker registered and activated successfully!', false, true);
      } catch (error) {
        log(`Error registering service worker: ${error.message}`);
        updateStatus('serviceWorkerStatus', `❌ Error registering service worker: ${error.message}`, true);
      }
    }

    async function testPushSubscription() {
      log('Testing push subscription (without Firebase)...');

      if (!serviceWorkerRegistration) {
        updateStatus('pushSubscriptionStatus', '❌ Service worker not registered. Please complete step 3 first.', true);
        return;
      }

      try {
        // Test if we can create a push subscription without VAPID
        log('Attempting to create push subscription...');
        const subscription = await serviceWorkerRegistration.pushManager.subscribe({
          userVisibleOnly: true
        });

        log('Push subscription created successfully');
        log(`Endpoint: ${subscription.endpoint}`);

        // Unsubscribe immediately since this is just a test
        await subscription.unsubscribe();
        log('Test subscription unsubscribed');

        updateStatus('pushSubscriptionStatus', '✅ Push subscription test successful! Your browser can handle push notifications.', false, true);
      } catch (error) {
        log(`Error creating push subscription: ${error.message}`);
        updateStatus('pushSubscriptionStatus', `❌ Push subscription test failed: ${error.message}. This indicates a browser or network issue.`, true);
      }
    }

    async function initializeFirebase() {
      log('Initializing Firebase...');

      try {
        // Load Firebase scripts dynamically
        if (!window.firebase) {
          log('Loading Firebase scripts...');
          await loadFirebaseScripts();
        }

        // Initialize Firebase with your configuration
        const firebaseConfig = {
          apiKey: "AIzaSyASiPlp08LrYJuAv4EEBEWVPlXJoLCZmWA",
          authDomain: "trackcircle-79f03.firebaseapp.com",
          projectId: "trackcircle-79f03",
          storageBucket: "trackcircle-79f03.firebasestorage.app",
          messagingSenderId: "732923593979",
          appId: "1:732923593979:web:075f5c537fc3a98da71c5e",
          measurementId: "G-NYZY4660TE"
        };

        if (!firebase.apps.length) {
          firebase.initializeApp(firebaseConfig);
          log('Firebase initialized');
        }

        // Check if messaging is supported
        if (firebase.messaging.isSupported()) {
          messaging = firebase.messaging();
          log('Firebase messaging is supported');
          updateStatus('firebaseStatus', '✅ Firebase initialized successfully and messaging is supported!', false, true);
        } else {
          log('Firebase messaging is not supported in this browser');
          updateStatus('firebaseStatus', '❌ Firebase messaging is not supported in this browser', true);
        }
      } catch (error) {
        log(`Error initializing Firebase: ${error.message}`);
        updateStatus('firebaseStatus', `❌ Error initializing Firebase: ${error.message}`, true);
      }
    }

    async function getFCMToken() {
      log('Getting FCM token...');

      if (!messaging) {
        updateStatus('tokenStatus', '❌ Firebase not initialized. Please complete step 5 first.', true);
        return;
      }

      if (!serviceWorkerRegistration) {
        updateStatus('tokenStatus', '❌ Service worker not registered. Please complete step 3 first.', true);
        return;
      }

      try {
        // Try without VAPID key first
        log('Attempting to get FCM token without VAPID key...');
        let token = await messaging.getToken({
          serviceWorkerRegistration: serviceWorkerRegistration
        });

        if (token) {
          log(`FCM token obtained: ${token}`);
          updateStatus('tokenStatus', `✅ FCM token obtained successfully!<br><strong>Token:</strong><br><code style="word-break: break-all;">${token}</code>`, false, true);
          return;
        }

        // If that fails, try with a VAPID key
        log('Attempting to get FCM token with VAPID key...');
        const vapidKey = "BBYIaOoQBnkURsdBdzY5zPcCSOZlh59mWCxqQKGpCH59HJC2Fq6k9F-N6oJc62lTBcogUVXpNYRHjLupPov3IM0";
        token = await messaging.getToken({
          vapidKey: vapidKey,
          serviceWorkerRegistration: serviceWorkerRegistration
        });

        if (token) {
          log(`FCM token obtained with VAPID: ${token}`);
          updateStatus('tokenStatus', `✅ FCM token obtained with VAPID key!<br><strong>Token:</strong><br><code style="word-break: break-all;">${token}</code>`, false, true);
        } else {
          log('No FCM token received');
          updateStatus('tokenStatus', '❌ No FCM token received. This may indicate a configuration issue.', true);
        }
      } catch (error) {
        log(`Error getting FCM token: ${error.message}`);
        updateStatus('tokenStatus', `❌ Error getting FCM token: ${error.message}`, true);
      }
    }

    function loadFirebaseScripts() {
      return new Promise((resolve, reject) => {
        const script1 = document.createElement('script');
        script1.src = 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js';
        script1.onload = () => {
          const script2 = document.createElement('script');
          script2.src = 'https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js';
          script2.onload = resolve;
          script2.onerror = reject;
          document.head.appendChild(script2);
        };
        script1.onerror = reject;
        document.head.appendChild(script1);
      });
    }

    // Initialize the page
    log('FCM Test page loaded');
  </script>
</body>
</html>
