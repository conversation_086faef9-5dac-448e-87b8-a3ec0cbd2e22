// Give the service worker access to Firebase Messaging.
// Note that you can only use Firebase Messaging here. Other Firebase libraries
// are not available in the service worker.
importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js');

// For testing purposes, log when the service worker is loaded
console.log('Firebase Messaging Service Worker Loaded');

// Initialize the Firebase app in the service worker by passing in
// your app's Firebase config object.
// https://firebase.google.com/docs/web/setup#config-object
// IMPORTANT: This must match the configuration in index.html
firebase.initializeApp({
  apiKey: "AIzaSyASiPlp08LrYJuAv4EEBEWVPlXJoLCZmWA",
  authDomain: "trackcircle-79f03.firebaseapp.com",
  projectId: "trackcircle-79f03",
  storageBucket: "trackcircle-79f03.firebasestorage.app",
  messagingSenderId: "732923593979",
  appId: "1:732923593979:web:075f5c537fc3a98da71c5e",
  measurementId: "G-NYZY4660TE"
});


// Log that Firebase has been initialized
console.log('[firebase-messaging-sw.js] Firebase initialized');

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);

  // Customize notification here
  const notificationTitle = payload.notification?.title || 'Background Message';
  const notificationOptions = {
    body: payload.notification?.body || 'Background Message body.',
    icon: '/firebase-logo.png',
    data: payload.data || {}
  };

  // Show notification
  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification clicked', event);

  // Close the notification
  event.notification.close();

  // Handle the click action - e.g., open a specific URL
  const clickAction = event.notification.data?.clickAction || '/';

  // This will open the app or focus an existing window
  event.waitUntil(
    clients.matchAll({type: 'window'}).then(windowClients => {
      // Check if there is already a window/tab open with the target URL
      for (let i = 0; i < windowClients.length; i++) {
        const client = windowClients[i];
        // If so, focus it
        if (client.url === clickAction && 'focus' in client) {
          return client.focus();
        }
      }
      // If not, open a new window/tab
      if (clients.openWindow) {
        return clients.openWindow(clickAction);
      }
    })
  );
});

// Log any errors
self.addEventListener('error', function(event) {
  console.error('Service Worker error:', event.message, event.filename, event.lineno);
});

// Log when the service worker is activated
self.addEventListener('activate', (event) => {
  console.log('Service Worker activated');
  // Claim control of all clients immediately
  event.waitUntil(clients.claim());
});

// Log when the service worker is installed
self.addEventListener('install', (event) => {
  console.log('Service Worker installed');
  // Force the service worker to activate immediately
  event.waitUntil(self.skipWaiting());
});
