<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FCM Message Receiver</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        padding: 20px;
        max-width: 800px;
        margin: 0 auto;
      }
      .container {
        border: 1px solid #ddd;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
      }
      h1,
      h2 {
        color: #333;
      }
      pre {
        background-color: #f4f4f4;
        padding: 10px;
        border-radius: 5px;
        overflow: auto;
        max-height: 300px;
      }
      button {
        background-color: #4caf50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        margin-top: 10px;
      }
      button:hover {
        background-color: #45a049;
      }
      .note {
        background-color: #fffde7;
        padding: 10px;
        border-left: 4px solid #ffd600;
        margin: 20px 0;
      }
      #messageLog {
        margin-top: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>FCM Message Receiver</h1>
      <p>
        This application receives and logs Firebase Cloud Messaging (FCM)
        messages. <a href="/fcm-sender.html" style="color: #4CAF50; font-weight: bold;">Go to FCM Sender</a> to send test messages.
      </p>

      <div class="note">
        <strong>Note:</strong> You need to allow notifications for this
        application to receive FCM messages. If you encounter errors, try the following:
        <ul>
          <li>Make sure you're using a supported browser (Chrome, Firefox, Edge)</li>
          <li>Ensure you have a valid Firebase configuration and VAPID key</li>
          <li>Try refreshing the page and clicking the "Request Notification Permission" button again</li>
          <li>Check the browser console for detailed error messages</li>
          <li>If using Chrome, make sure you're using HTTPS or localhost</li>
        </ul>
      </div>

      <div class="note" style="background-color: #e8f5e9; border-left-color: #4CAF50;">
        <strong>Important:</strong> For this demo to work properly, you need to:
        <ol>
          <li>Replace the Firebase configuration in the code with your own</li>
          <li>Replace the VAPID key with your own from Firebase Console</li>
          <li>Make sure your Firebase project has Cloud Messaging enabled</li>
        </ol>
      </div>

      <button id="requestPermission">Request Notification Permission</button>

      <h2>FCM Token</h2>
      <pre id="tokenDisplay">Requesting token...</pre>

      <h2>Message Log</h2>
      <pre id="messageLog">No messages received yet.</pre>
    </div>

    <!-- Firebase App (the core Firebase SDK) -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <!-- Firebase Messaging -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-messaging-compat.js"></script>

    <script>
      // Your web app's Firebase configuration
      // IMPORTANT: Replace this with your own Firebase configuration
      const firebaseConfig = {
        apiKey: "AIzaSyASiPlp08LrYJuAv4EEBEWVPlXJoLCZmWA",
        authDomain: "trackcircle-79f03.firebaseapp.com",
        projectId: "trackcircle-79f03",
        storageBucket: "trackcircle-79f03.firebasestorage.app",
        messagingSenderId: "732923593979",
        appId: "1:732923593979:web:075f5c537fc3a98da71c5e",
        measurementId: "G-NYZY4660TE",
      };

      // IMPORTANT: Replace this with your own VAPID key from Firebase Console
      // Go to Project Settings > Cloud Messaging > Web Push certificates
      const vapidKey = "BBYIaOoQBnkURsdBdzY5zPcCSOZlh59mWCxqQKGpCH59HJC2Fq6k9F-N6oJc62lTBcogUVXpNYRHjLupPov3IM0";

      // Initialize Firebase
      firebase.initializeApp(firebaseConfig);

      // Get DOM elements early to avoid reference errors
      const tokenDisplay = document.getElementById("tokenDisplay");
      const messageLog = document.getElementById("messageLog");
      const requestPermissionButton = document.getElementById("requestPermission");

      // Function to unregister existing service workers
      async function unregisterServiceWorkers() {
        try {
          const registrations = await navigator.serviceWorker.getRegistrations();
          console.log(`Found ${registrations.length} service worker registrations`);

          for (let registration of registrations) {
            console.log(`Unregistering service worker with scope: ${registration.scope}`);
            await registration.unregister();
            console.log('Service worker unregistered successfully');
          }
          return true;
        } catch (error) {
          console.error('Error unregistering service workers:', error);
          return false;
        }
      }

      // Register service worker
      async function registerServiceWorker() {
        if (!('serviceWorker' in navigator)) {
          console.error('Service workers are not supported in this browser');
          tokenDisplay.textContent = 'Service workers not supported';
          return null;
        }

        try {
          // Unregister any existing service workers to ensure a clean state
          await unregisterServiceWorkers();

          console.log('Registering new Service Worker...');
          const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
            scope: '/'
          });

          console.log('Service Worker registration successful with scope:', registration.scope);

          // Wait for the service worker to be activated
          if (registration.installing) {
            console.log('Service Worker is installing...');

            // Wait for the service worker to be activated
            await new Promise((resolve) => {
              registration.installing.addEventListener('statechange', (event) => {
                if (event.target.state === 'activated') {
                  console.log('Service Worker is now activated');
                  resolve();
                }
              });
            });
          } else if (registration.waiting) {
            console.log('Service Worker is waiting...');
          } else if (registration.active) {
            console.log('Service Worker is already active');
          }

          // Get messaging instance - Firebase will automatically use the registered service worker
          const messaging = firebase.messaging();

          // Continue with the rest of the code
          initApp(messaging);

          return registration;
        } catch (error) {
          console.error('Service Worker registration failed:', error);
          tokenDisplay.textContent = `Service Worker Error: ${error.message}`;
          return null;
        }
      }

      // Start the registration process
      registerServiceWorker();

      // DOM elements are already defined above

      // Function to initialize the app after service worker is registered
      function initApp(messaging) {

      // Function to convert base64 string to Uint8Array for applicationServerKey
      function urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
          .replace(/\-/g, '+')
          .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
          outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
      }

      // Function to request permission and get token
      async function requestPermissionAndGetToken() {
        tokenDisplay.textContent = "Requesting permission and token...";

        try {
          // Check if Firebase Messaging is supported in this browser
          if (!firebase.messaging.isSupported()) {
            console.error("Firebase Messaging is not supported in this browser");
            tokenDisplay.textContent = "Firebase Messaging not supported in this browser";
            return null;
          }

          // Request permission
          console.log("Requesting notification permission...");
          const permission = await Notification.requestPermission();
          console.log(`Notification permission ${permission}`);

          if (permission !== "granted") {
            console.log("Notification permission denied.");
            tokenDisplay.textContent = "Notification permission denied. Please enable notifications.";
            return null;
          }

          console.log("Notification permission granted.");

          // Make sure service worker is registered and active
          console.log("Checking service worker registration...");
          const registrations = await navigator.serviceWorker.getRegistrations();
          let swRegistration = null;

          for (const reg of registrations) {
            console.log(`Found service worker registration with scope: ${reg.scope}`);
            if (reg.scope.includes(window.location.origin)) {
              swRegistration = reg;
              break;
            }
          }

          if (!swRegistration) {
            console.log("No suitable service worker found, registering a new one...");
            // Try registering again
            swRegistration = await navigator.serviceWorker.register('/firebase-messaging-sw.js', {
              scope: '/'
            });

            // Wait for it to activate if it's installing
            if (swRegistration.installing) {
              console.log("Waiting for service worker to activate...");
              await new Promise(resolve => {
                swRegistration.installing.addEventListener('statechange', e => {
                  if (e.target.state === 'activated') {
                    console.log("Service worker now activated");
                    resolve();
                  }
                });
              });
            }
          }

          console.log("Service worker registration confirmed:", swRegistration);

          // Get token with VAPID key
          console.log("Getting FCM token with VAPID key...");
          tokenDisplay.textContent = "Getting FCM token...";

          try {
            // Clear any existing tokens from IndexedDB
            console.log("Attempting to clear any existing tokens...");
            await messaging.deleteToken();
            console.log("Existing token cleared or none existed");
          } catch (deleteError) {
            console.log("No existing token to delete or error deleting token:", deleteError);
            // Continue anyway
          }

          try {
            // Get a new token
            console.log("Requesting new FCM token...");
            const currentToken = await messaging.getToken({
              vapidKey: vapidKey,
              serviceWorkerRegistration: swRegistration
            });

            if (currentToken) {
              console.log("FCM Token obtained successfully:", currentToken);
              tokenDisplay.textContent = currentToken;
              return currentToken;
            } else {
              console.error("No FCM token received");
              tokenDisplay.textContent = "Failed to get FCM token. No token received.";
              return null;
            }
          } catch (tokenError) {
            console.error("Error getting FCM token:", tokenError);
            tokenDisplay.textContent = `Error getting token: ${tokenError.message}`;

            // Try one more time with just the VAPID key
            try {
              console.log("Trying one more time with just VAPID key...");
              const fallbackToken = await messaging.getToken({
                vapidKey: vapidKey
              });

              if (fallbackToken) {
                console.log("FCM Token obtained with fallback method:", fallbackToken);
                tokenDisplay.textContent = fallbackToken;
                return fallbackToken;
              } else {
                tokenDisplay.textContent = "All token retrieval methods failed.";
                return null;
              }
            } catch (fallbackError) {
              console.error("Fallback token retrieval also failed:", fallbackError);
              tokenDisplay.textContent = `All token retrieval methods failed: ${tokenError.message}; ${fallbackError.message}`;
              return null;
            }
          }
        } catch (error) {
          console.error("Error in requestPermissionAndGetToken:", error);
          tokenDisplay.textContent = `Error: ${error.message}`;
          return null;
        }
      }

      // Set up message listener
      try {
        messaging.onMessage((payload) => {
          console.log("Message received:", payload);

          // Format the message for display
          const timestamp = new Date().toISOString();
          const formattedMessage = `[${timestamp}] Received message:\n${JSON.stringify(
            payload,
            null,
            2
          )}`;

          // Update message log
          if (messageLog.textContent === "No messages received yet.") {
            messageLog.textContent = formattedMessage;
          } else {
            messageLog.textContent =
              formattedMessage + "\n\n" + messageLog.textContent;
          }

          // Display notification
          const notificationTitle = payload.notification?.title || "New Message";
          const notificationOptions = {
            body: payload.notification?.body || "You have a new message",
            icon: "/firebase-logo.png",
            data: payload.data,
          };

          new Notification(notificationTitle, notificationOptions);
        });
        console.log("FCM message listener set up successfully");
      } catch (error) {
        console.error("Error setting up message listener:", error);
      }

      // Add event listener to request permission button
      requestPermissionButton.addEventListener(
        "click",
        requestPermissionAndGetToken
      );

      // Request permission on page load
      document.addEventListener(
        "DOMContentLoaded",
        requestPermissionAndGetToken
      );

      } // Close initApp function
    </script>
  </body>
</html>
