const express = require('express');
const admin = require('firebase-admin');
const router = express.Router();

// API endpoint to send FCM message to a specific device
router.post('/send', async (req, res) => {
  try {
    console.log('Direct API send endpoint called with body:', req.body);
    
    // Validate request body
    const { token, title, body, data } = req.body;
    
    if (!token) {
      return res.status(400).json({ success: false, error: 'FCM token is required' });
    }

    // Check if Firebase Admin SDK is initialized
    try {
      admin.app();
    } catch (e) {
      return res.status(500).json({ 
        success: false, 
        error: 'Firebase Admin SDK not initialized. Make sure serviceAccountKey.json exists.' 
      });
    }

    // Create message payload
    const message = {
      token: token,
      notification: {
        title: title || 'New Message',
        body: body || 'You have a new message'
      },
      data: data || {},
      android: {
        priority: 'high'
      },
      apns: {
        payload: {
          aps: {
            contentAvailable: true
          }
        }
      },
      webpush: {
        headers: {
          Urgency: 'high'
        }
      }
    };

    // Log the message being sent
    console.log('Sending FCM message:', JSON.stringify(message, null, 2));

    // Send the message
    const response = await admin.messaging().send(message);
    
    // Log success
    console.log('Successfully sent message:', response);
    
    // Return success response
    return res.status(200).json({ 
      success: true, 
      messageId: response,
      message: 'Message sent successfully' 
    });
  } catch (error) {
    // Log error
    console.error('Error sending message:', error);
    
    // Return error response
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'Unknown error occurred'
    });
  }
});

module.exports = router;
