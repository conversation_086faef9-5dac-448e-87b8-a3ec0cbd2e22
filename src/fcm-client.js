// This module is designed to be used in a browser environment
// In Node.js, we'll provide mock implementations

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined' && typeof navigator !== 'undefined';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyASiPlp08LrYJuAv4EEBEWVPlXJoLCZmWA",
  authDomain: "trackcircle-79f03.firebaseapp.com",
  projectId: "trackcircle-79f03",
  storageBucket: "trackcircle-79f03.firebasestorage.app",
  messagingSenderId: "732923593979",
  appId: "1:732923593979:web:075f5c537fc3a98da71c5e",
  measurementId: "G-NYZY4660TE"
};

// Initialize Firebase and Messaging only in browser environment
let firebaseApp;
let messaging;

if (isBrowser) {
  const { initializeApp } = require('firebase/app');
  const { getMessaging } = require('firebase/messaging');

  firebaseApp = initializeApp(firebaseConfig);
  messaging = getMessaging(firebaseApp);
}

// Function to convert base64 string to Uint8Array for applicationServerKey
function urlBase64ToUint8Array(base64String) {
  if (typeof window === 'undefined') return null;

  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/\-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// Function to request permission and get FCM token
async function requestPermissionAndGetToken() {
  console.log('Requesting permission...');

  // Only run in browser environment
  if (!isBrowser) {
    console.log('Not in browser environment. Cannot request permission.');
    return null;
  }

  try {
    // Import getToken only in browser environment
    const { getToken } = require('firebase/messaging');

    // Request permission for notifications
    const permission = await Notification.requestPermission();

    if (permission === 'granted') {
      console.log('Notification permission granted.');

      // Get FCM token
      try {
        // Your VAPID key from Firebase console (Web Push certificate)
        const vapidKey = "BKagOny0KF_2pCJQ3m0I4XKcmO7K0C9GhNAAfvmNGaGXZZaOJTsyBgJVfj-9xTMkuUkZvLGLQrYzUjC9kIEWrZ8";

        const currentToken = await getToken(messaging, {
          vapidKey: vapidKey
        });

        if (currentToken) {
          console.log('FCM Token:', currentToken);
          return currentToken;
        } else {
          console.log('No registration token available. Request permission to generate one.');
          return null;
        }
      } catch (err) {
        console.error('An error occurred while retrieving token:', err);
        return null;
      }
    } else {
      console.log('Notification permission denied.');
      return null;
    }
  } catch (error) {
    console.error('Error requesting permission:', error);
    return null;
  }
}

// Function to set up FCM message listener
function setupMessageListener() {
  console.log('Setting up FCM message listener...');

  // Only run in browser environment
  if (!isBrowser) {
    console.log('Not in browser environment. Cannot set up FCM message listener.');
    return;
  }

  try {
    // Import onMessage only in browser environment
    const { onMessage } = require('firebase/messaging');

    // Listen for incoming FCM messages
    onMessage(messaging, (payload) => {
      console.log('Message received:', payload);

      // Log message details
      const timestamp = new Date().toISOString();
      console.log(`\n[${timestamp}] Received FCM message:`);
      console.log('----------------------------------------');
      console.log(JSON.stringify(payload, null, 2));

      // Log notification if available
      if (payload.notification) {
        console.log('\nNotification:');
        console.log(`Title: ${payload.notification.title || 'N/A'}`);
        console.log(`Body: ${payload.notification.body || 'N/A'}`);
      }

      // Log data payload if available
      if (payload.data) {
        console.log('\nData Payload:');
        console.log(JSON.stringify(payload.data, null, 2));
      }

      console.log('----------------------------------------\n');

      // Display notification
      const notificationTitle = payload.notification?.title || 'New Message';
      const notificationOptions = {
        body: payload.notification?.body || 'You have a new message',
        icon: '/firebase-logo.png',
        data: payload.data
      };

      new Notification(notificationTitle, notificationOptions);
    });

    console.log('FCM message listener set up successfully');
  } catch (error) {
    console.error('Error setting up FCM message listener:', error);
  }
}

module.exports = {
  requestPermissionAndGetToken,
  setupMessageListener
};
