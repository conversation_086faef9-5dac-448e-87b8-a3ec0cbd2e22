const admin = require('firebase-admin');
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Create Express router
const router = express.Router();

// Middleware
router.use(cors());
router.use(bodyParser.json());

// Debug middleware for FCM sender router
router.use((req, res, next) => {
  console.log(`[FCM Sender] ${req.method} ${req.path} - Body:`, JSON.stringify(req.body));
  next();
});

// Check if Firebase Admin SDK is initialized
const isFirebaseInitialized = () => {
  try {
    admin.app();
    return true;
  } catch (e) {
    return false;
  }
};

// Initialize Firebase Admin SDK if not already initialized
const initializeFirebaseAdmin = () => {
  if (!isFirebaseInitialized()) {
    try {
      // Check if service account file exists
      const serviceAccountPath = path.join(__dirname, '../serviceAccountKey.json');

      if (fs.existsSync(serviceAccountPath)) {
        const serviceAccount = require('../serviceAccountKey.json');

        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount)
        });

        console.log('Firebase Admin SDK initialized successfully in FCM sender');
        return true;
      } else {
        console.error('serviceAccountKey.json not found. Cannot send FCM messages.');
        return false;
      }
    } catch (error) {
      console.error('Error initializing Firebase Admin SDK:', error.message);
      return false;
    }
  }
  return true;
};

// API endpoint to send FCM message to a specific device
router.post('/send-to-device', async (req, res) => {
  try {
    // Initialize Firebase Admin SDK if not already initialized
    if (!initializeFirebaseAdmin()) {
      return res.status(500).json({
        success: false,
        error: 'Firebase Admin SDK not initialized. Make sure serviceAccountKey.json exists.'
      });
    }

    // Validate request body
    const { token, title, body, data } = req.body;

    if (!token) {
      return res.status(400).json({ success: false, error: 'FCM token is required' });
    }

    // Create message payload
    const message = {
      token: token,
      notification: {
        title: title || 'New Message',
        body: body || 'You have a new message'
      },
      data: data || {},
      android: {
        priority: 'high'
      },
      apns: {
        payload: {
          aps: {
            contentAvailable: true
          }
        }
      },
      webpush: {
        headers: {
          Urgency: 'high'
        }
      }
    };

    // Log the message being sent
    console.log('Sending FCM message:', JSON.stringify(message, null, 2));

    // Send the message
    const response = await admin.messaging().send(message);

    // Log success
    console.log('Successfully sent message:', response);

    // Return success response
    return res.status(200).json({
      success: true,
      messageId: response,
      message: 'Message sent successfully'
    });
  } catch (error) {
    // Log error
    console.error('Error sending message:', error);

    // Return error response
    return res.status(500).json({
      success: false,
      error: error.message || 'Unknown error occurred'
    });
  }
});

// API endpoint to send FCM message to multiple devices
router.post('/send-to-devices', async (req, res) => {
  try {
    // Initialize Firebase Admin SDK if not already initialized
    if (!initializeFirebaseAdmin()) {
      return res.status(500).json({
        success: false,
        error: 'Firebase Admin SDK not initialized. Make sure serviceAccountKey.json exists.'
      });
    }

    // Validate request body
    const { tokens, title, body, data } = req.body;

    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      return res.status(400).json({ success: false, error: 'Array of FCM tokens is required' });
    }

    // Create message payload
    const message = {
      tokens: tokens,
      notification: {
        title: title || 'New Message',
        body: body || 'You have a new message'
      },
      data: data || {},
      android: {
        priority: 'high'
      },
      apns: {
        payload: {
          aps: {
            contentAvailable: true
          }
        }
      },
      webpush: {
        headers: {
          Urgency: 'high'
        }
      }
    };

    // Log the message being sent
    console.log('Sending FCM message to multiple devices:', JSON.stringify(message, null, 2));

    // Send the message
    const response = await admin.messaging().sendMulticast(message);

    // Log success
    console.log('Successfully sent multicast message:', response);

    // Return success response
    return res.status(200).json({
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      responses: response.responses,
      message: 'Messages sent successfully'
    });
  } catch (error) {
    // Log error
    console.error('Error sending multicast message:', error);

    // Return error response
    return res.status(500).json({
      success: false,
      error: error.message || 'Unknown error occurred'
    });
  }
});

// API endpoint to send FCM message to a topic
router.post('/send-to-topic', async (req, res) => {
  try {
    // Initialize Firebase Admin SDK if not already initialized
    if (!initializeFirebaseAdmin()) {
      return res.status(500).json({
        success: false,
        error: 'Firebase Admin SDK not initialized. Make sure serviceAccountKey.json exists.'
      });
    }

    // Validate request body
    const { topic, title, body, data } = req.body;

    if (!topic) {
      return res.status(400).json({ success: false, error: 'Topic name is required' });
    }

    // Create message payload
    const message = {
      topic: topic,
      notification: {
        title: title || 'New Message',
        body: body || 'You have a new message'
      },
      data: data || {},
      android: {
        priority: 'high'
      },
      apns: {
        payload: {
          aps: {
            contentAvailable: true
          }
        }
      },
      webpush: {
        headers: {
          Urgency: 'high'
        }
      }
    };

    // Log the message being sent
    console.log('Sending FCM message to topic:', JSON.stringify(message, null, 2));

    // Send the message
    const response = await admin.messaging().send(message);

    // Log success
    console.log('Successfully sent message to topic:', response);

    // Return success response
    return res.status(200).json({
      success: true,
      messageId: response,
      message: 'Message sent to topic successfully'
    });
  } catch (error) {
    // Log error
    console.error('Error sending message to topic:', error);

    // Return error response
    return res.status(500).json({
      success: false,
      error: error.message || 'Unknown error occurred'
    });
  }
});

// API endpoint to subscribe devices to a topic
router.post('/subscribe-to-topic', async (req, res) => {
  try {
    // Initialize Firebase Admin SDK if not already initialized
    if (!initializeFirebaseAdmin()) {
      return res.status(500).json({
        success: false,
        error: 'Firebase Admin SDK not initialized. Make sure serviceAccountKey.json exists.'
      });
    }

    // Validate request body
    const { tokens, topic } = req.body;

    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      return res.status(400).json({ success: false, error: 'Array of FCM tokens is required' });
    }

    if (!topic) {
      return res.status(400).json({ success: false, error: 'Topic name is required' });
    }

    // Subscribe to the topic
    const response = await admin.messaging().subscribeToTopic(tokens, topic);

    // Log success
    console.log('Successfully subscribed to topic:', response);

    // Return success response
    return res.status(200).json({
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      errors: response.errors,
      message: 'Devices subscribed to topic successfully'
    });
  } catch (error) {
    // Log error
    console.error('Error subscribing to topic:', error);

    // Return error response
    return res.status(500).json({
      success: false,
      error: error.message || 'Unknown error occurred'
    });
  }
});

// API endpoint to unsubscribe devices from a topic
router.post('/unsubscribe-from-topic', async (req, res) => {
  try {
    // Initialize Firebase Admin SDK if not already initialized
    if (!initializeFirebaseAdmin()) {
      return res.status(500).json({
        success: false,
        error: 'Firebase Admin SDK not initialized. Make sure serviceAccountKey.json exists.'
      });
    }

    // Validate request body
    const { tokens, topic } = req.body;

    if (!tokens || !Array.isArray(tokens) || tokens.length === 0) {
      return res.status(400).json({ success: false, error: 'Array of FCM tokens is required' });
    }

    if (!topic) {
      return res.status(400).json({ success: false, error: 'Topic name is required' });
    }

    // Unsubscribe from the topic
    const response = await admin.messaging().unsubscribeFromTopic(tokens, topic);

    // Log success
    console.log('Successfully unsubscribed from topic:', response);

    // Return success response
    return res.status(200).json({
      success: true,
      successCount: response.successCount,
      failureCount: response.failureCount,
      errors: response.errors,
      message: 'Devices unsubscribed from topic successfully'
    });
  } catch (error) {
    // Log error
    console.error('Error unsubscribing from topic:', error);

    // Return error response
    return res.status(500).json({
      success: false,
      error: error.message || 'Unknown error occurred'
    });
  }
});

module.exports = router;
