const express = require('express');
const path = require('path');
const admin = require('firebase-admin');
const fs = require('fs');
const fcmSender = require('./fcm-sender');
const directApi = require('./direct-api');
const testApi = require('./test-api');

// Initialize Express app
const app = express();

// Debug middleware to log all requests
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  next();
});

// Parse JSON request bodies
app.use(express.json());

// Serve static files
app.use(express.static(path.join(__dirname, '../public')));

// Mount FCM sender API routes
app.use('/api/fcm', fcmSender);

// Mount direct API routes
app.use('/api/direct', directApi);

// Log that the APIs are mounted
console.log('FCM sender API mounted at /api/fcm');
console.log('Direct API mounted at /api/direct');

// Set up port
const PORT = process.env.PORT || 3000;

// Initialize Firebase Admin SDK for server-side operations
try {
  // Check if service account file exists
  const fs = require('fs');
  const serviceAccountPath = path.join(__dirname, '../serviceAccountKey.json');

  if (fs.existsSync(serviceAccountPath)) {
    const serviceAccount = require('../serviceAccountKey.json');

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });

    console.log('Firebase Admin SDK initialized successfully');
  } else {
    console.warn('serviceAccountKey.json not found. Running in development mode without Firebase authentication.');
    console.warn('To receive actual FCM messages, please create a serviceAccountKey.json file.');
    console.warn('See README.md for instructions.');
  }
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error.message);
  console.error('Please make sure you have a valid serviceAccountKey.json file in the root directory');
}

// Log that we're running in Node.js environment
console.log('Running in Node.js environment. FCM client-side listener not available.');
console.log('Messages will be received through the /fcm/receive endpoint.');

// Note: The FCM client-side listener will be set up in the browser when the HTML page is loaded

// Function to log messages both to console and file
function logMessage(message) {
  console.log(message);

  // Create logs directory if it doesn't exist
  const logsDir = path.join(__dirname, '../logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  // Append to log file
  const logFile = path.join(logsDir, 'fcm_messages.log');
  fs.appendFileSync(logFile, message + '\n');
}

// Endpoint to receive messages from another app
app.post('/fcm/receive', (req, res) => {
  const timestamp = new Date().toISOString();

  logMessage(`\n[${timestamp}] Received message:`);
  logMessage('----------------------------------------');
  logMessage(JSON.stringify(req.body, null, 2));

  // Log additional information if available
  if (req.body.message) {
    if (req.body.message.data) {
      logMessage('\nMessage data:');
      logMessage(JSON.stringify(req.body.message.data, null, 2));
    }

    if (req.body.message.notification) {
      logMessage('\nNotification:');
      logMessage(`Title: ${req.body.message.notification.title || 'N/A'}`);
      logMessage(`Body: ${req.body.message.notification.body || 'N/A'}`);
    }
  }

  // Handle FCM-specific format
  if (req.body.data) {
    logMessage('\nFCM Data Payload:');
    logMessage(JSON.stringify(req.body.data, null, 2));
  }

  if (req.body.notification) {
    logMessage('\nFCM Notification:');
    logMessage(`Title: ${req.body.notification.title || 'N/A'}`);
    logMessage(`Body: ${req.body.notification.body || 'N/A'}`);
  }

  logMessage('----------------------------------------\n');

  // Respond with success
  res.status(200).json({ success: true, message: 'Message received and logged', timestamp });
});

// Also handle GET requests to the FCM endpoint (for browser testing and verification)
app.get('/fcm/receive', (_, res) => {
  res.status(200).send(`
    <html>
      <head>
        <title>FCM Receiver</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; padding: 20px; max-width: 800px; margin: 0 auto; }
          pre { background-color: #f4f4f4; padding: 10px; border-radius: 5px; overflow: auto; }
          .container { border: 1px solid #ddd; padding: 20px; border-radius: 5px; }
          h1 { color: #333; }
          .note { background-color: #fffde7; padding: 10px; border-left: 4px solid #ffd600; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1>Message Receiver Endpoint</h1>
          <p>This endpoint is configured to receive and log messages from your other application.</p>

          <div class="note">
            <strong>Note:</strong> This endpoint expects POST requests with JSON payloads.
            You cannot test it directly in a browser which uses GET requests.
          </div>

          <h2>How to Test</h2>
          <p>You can test this endpoint using cURL or any API testing tool like Postman:</p>

          <pre>curl -X POST http://localhost:${PORT}/fcm/receive \
  -H "Content-Type: application/json" \
  -d '{
    "message": {
      "data": {
        "key": "value"
      },
      "notification": {
        "title": "Test Title",
        "body": "Test Body"
      }
    }
  }'</pre>

          <p>Check your server console to see the logged message.</p>
        </div>
      </body>
    </html>
  `);
});

// Root route - redirect to the HTML page
app.get('/', (_, res) => {
  res.redirect('/index.html');
});

// Health check endpoint
app.get('/health', (_, res) => {
  res.status(200).json({ status: 'ok' });
});


// Test endpoint
app.post('/test', (req, res) => {
  console.log('Test endpoint called with body:', req.body);
  res.status(200).json({ success: true, message: 'Test endpoint called successfully', body: req.body });
});

// Another test endpoint with a different path
app.post('/api/test', (req, res) => {
  console.log('API test endpoint called with body:', req.body);
  res.status(200).json({ success: true, message: 'API test endpoint called successfully', body: req.body });
});

// Endpoint to get Firebase configuration for the client
app.get('/api/firebase-config', (req, res) => {
  try {
    // Check if Firebase Admin SDK is initialized
    let firebaseApp;
    try {
      firebaseApp = admin.app();
    } catch (e) {
      return res.status(500).json({
        success: false,
        error: 'Firebase Admin SDK not initialized. Make sure serviceAccountKey.json exists.'
      });
    }

    // Get the project ID from the Firebase app
    const projectId = firebaseApp.options.projectId;

    if (!projectId) {
      return res.status(500).json({
        success: false,
        error: 'Project ID not found in Firebase configuration.'
      });
    }

    // Return the Firebase configuration that the client should use
    const clientConfig = {
      projectId: projectId,
      messagingSenderId: firebaseApp.options.projectId, // This might need to be different
      // Note: We're not including the full config here for security reasons
      // The client will need to use their own Firebase web app config
    };

    res.status(200).json({
      success: true,
      config: clientConfig,
      message: 'Firebase configuration retrieved successfully'
    });
  } catch (error) {
    console.error('Error getting Firebase config:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Unknown error occurred'
    });
  }
});

// Start the server
app.listen(PORT, () => {
  logMessage(`FCM receiver server running on port ${PORT}`);
  logMessage(`Endpoint for receiving FCM messages: http://localhost:${PORT}/fcm/receive`);
  logMessage(`Server started at ${new Date().toISOString()}`);
});
