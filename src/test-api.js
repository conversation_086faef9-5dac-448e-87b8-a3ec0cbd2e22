const express = require('express');
const router = express.Router();

// Simple test endpoint
router.post('/simple', (req, res) => {
  console.log('Simple test endpoint called with body:', req.body);
  res.status(200).json({ 
    success: true, 
    message: 'Simple test endpoint called successfully', 
    body: req.body 
  });
});

// Echo endpoint
router.post('/echo', (req, res) => {
  console.log('Echo endpoint called with body:', req.body);
  res.status(200).json(req.body);
});

// Status endpoint
router.get('/status', (req, res) => {
  res.status(200).json({ status: 'ok', time: new Date().toISOString() });
});

module.exports = router;
